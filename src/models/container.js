'use strict';

const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Container extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // Container has many InventoryItems
      Container.hasMany(models.InventoryItem, {
        foreignKey: 'container_id',
        as: 'inventoryItems',
        onDelete: 'CASCADE'
      });
    }
  }

  Container.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    container_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: {
        name: 'container_number_unique',
        msg: 'Container number must be unique'
      },
      validate: {
        notEmpty: {
          msg: 'Container number is required'
        },
        len: {
          args: [1, 50],
          msg: 'Container number must be between 1 and 50 characters'
        }
      }
    },
    arrival_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      validate: {
        notNull: {
          msg: 'Arrival date is required'
        },
        isDate: {
          msg: 'Arrival date must be a valid date'
        }
      }
    },
    status: {
      type: DataTypes.ENUM('pending', 'arrived', 'processed'),
      allowNull: false,
      defaultValue: 'pending',
      validate: {
        isIn: {
          args: [['pending', 'arrived', 'processed']],
          msg: 'Status must be one of: pending, arrived, processed'
        }
      }
    },
    total_items: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        isInt: {
          msg: 'Total items must be an integer'
        },
        min: {
          args: [0],
          msg: 'Total items must be non-negative'
        }
      }
    }
  }, {
    sequelize,
    modelName: 'Container',
    tableName: 'containers',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['container_number']
      },
      {
        fields: ['status']
      },
      {
        fields: ['arrival_date']
      }
    ]
  });

  return Container;
};
